<?php

/**
 * Scheduled task definitions for Importacao historico
 *
 * Documentation: {@link https://moodledev.io/docs/apis/subsystems/task}
 *
 * @package importacao_historico
 * @category task
 * <AUTHOR> Raphael (PH)
 */

defined('MOODLE_INTERNAL') || die();

$tasks = [
    array(
        'classname' => 'local_importacao_historico\tasks\importacao',
        'blocking' => 0,
        'minute' => '*',
        'hour' => '1',
        'day' => '*',
        'month' => '*',
        'dayofweek' => '*',
        'disabled' => 1 // Desabilitada por padrão
    ),
    array(
        'classname' => 'local_importacao_historico\tasks\importacao_usuario',
        'blocking' => 0,
        'minute' => '*',
        'hour' => '1',
        'day' => '*',
        'month' => '*',
        'dayofweek' => '*',
        'disabled' => 1 // Desabilitada por padrão
    ),
    array(
        'classname' => 'local_importacao_historico\tasks\importacao_grade',
        'blocking' => 0,
        'minute' => '*',
        'hour' => '1',
        'day' => '*',
        'month' => '*',
        'dayofweek' => '*',
        'disabled' => 1 // Desabilitada por padrão
    ),
    array(
        'classname' => 'local_importacao_historico\tasks\importacao_certificado',
        'blocking' => 0,
        'minute' => '*',
        'hour' => '1',
        'day' => '*',
        'month' => '*',
        'dayofweek' => '*',
        'disabled' => 1 // Desabilitada por padrão
    ),
    array(
        'classname' => 'local_importacao_historico\tasks\importacao_conclusao',
        'blocking' => 0,
        'minute' => '*',
        'hour' => '1',
        'day' => '*',
        'month' => '*',
        'dayofweek' => '*',
        'disabled' => 1 // Desabilitada por padrão
    ),
];
