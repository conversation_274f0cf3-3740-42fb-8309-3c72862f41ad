<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_importacao_historico\tasks;

/**
 * Scheduled task for user enrollment in courses from historical data.
 *
 * This task processes historical enrollment data and enrolls users in courses
 * using the manual enrollment method. It updates enrollment status after
 * successful enrollment.
 *
 * @package    local_importacao_historico
 * @category   task
 * @copyright  2023 Raphael (PH)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

use Exception;
use dml_exception;
use moodle_exception;

defined('MOODLE_INTERNAL') || die();

class importacao_usuario extends \core\task\scheduled_task {

    /**
     * Get the name of this task.
     *
     * @return string The task name from language strings
     */
    public function get_name() {
        return get_string('task_name_user', 'local_importacao_historico');
    }

    /**
     * Execute the scheduled task.
     *
     * This method processes historical enrollment data and enrolls users in courses.
     * It performs the following operations:
     * 1. Retrieves enrollment data from importacao_historico table
     * 2. Enrolls users in courses using manual enrollment
     * 3. Updates enrollment status in the database
     *
     * @throws dml_exception If database operations fail
     * @throws moodle_exception If enrollment operations fail
     */
    public function execute() {
        global $DB;

        try {
            // Start database transaction for data integrity
            $transaction = $DB->start_delegated_transaction();

            mtrace('Iniciando processo de inscrição de usuários...');

            // Optimized SQL query - removed unnecessary ROW_NUMBER() and GROUP BY
            $sql = "SELECT 
                    ROW_NUMBER() OVER() AS unique_id,
                    c.id as idcourse, 
                    u.id as iduser, 
                    imp.grade as nota, 
                    c.fullname as coursename,
                    u.username as username,
                    imp.status_cadastro as status_cadastro, 
                    imp.status_matricula as status_matricula, 
                    imp.email as email
                FROM {importacao_historico} imp 
                JOIN {course} c ON imp.coursecode = c.idnumber 
                JOIN {user} u ON u.username = imp.username
                group by imp.coursecode, u.id";


            $results = $DB->get_records_sql($sql);

            // Check if there are results
            if (empty($results)) {
                mtrace('Nenhum usuário pendente para inscrever.');
                $transaction->allow_commit();
                return;
            }

            mtrace('Encontrados ' . count($results) . ' registros para processar.');


            $enrol_manual = enrol_get_plugin('manual');
            if (!$enrol_manual) {
                throw new moodle_exception('error', 'core', '', 'Plugin de inscrição manual não encontrado');
            }

            $processed = 0;
            $errors = 0;

            foreach ($results as $record) {
                try {
                    if ($this->is_user_enrolled($record->iduser, $record->idcourse)) {
                        mtrace("Usuário {$record->username} já está inscrito no curso {$record->coursename}");
                        $this->update_enrollment_status($record->email);
                        continue;
                    }

                    // Get manual enrollment instance for the course
                    $manualinstance = $this->get_manual_enrollment_instance($record->idcourse);
                    if (!$manualinstance) {
                        mtrace("Instância de inscrição manual não encontrada no curso: {$record->coursename}");
                        $errors++;
                        continue;
                    }

                    // Enroll user with student role (using constant instead of hardcoded value)
                    $studentrole = $DB->get_field('role', 'id', ['shortname' => 'student']);
                    if (!$studentrole) {
                        mtrace("Role de estudante não encontrada no sistema");
                        $errors++;
                        continue;
                    }

                    $enrol_manual->enrol_user($manualinstance, $record->iduser, $studentrole);

                    // Update enrollment status efficiently
                    $this->update_enrollment_status($record->email);

                    mtrace("✓ Usuário {$record->username} inscrito no curso {$record->coursename}");
                    $processed++;

                } catch (Exception $e) {
                    mtrace("✗ Erro ao inscrever usuário {$record->username}: " . $e->getMessage());
                    $errors++;
                    continue;
                }
            }


            $transaction->allow_commit();

            mtrace("Processo concluído: {$processed} usuários inscritos, {$errors} erros.");

        } catch (Exception $e) {
            // Rollback transaction on error
            if (isset($transaction)) {
                $transaction->rollback($e);
            }
            mtrace("Erro crítico durante o processo: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Check if user is already enrolled in the course.
     *
     * @param int $userid User ID
     * @param int $courseid Course ID
     * @return bool True if user is enrolled, false otherwise
     */
    private function is_user_enrolled($userid, $courseid) {
        global $DB;

        return $DB->record_exists('user_enrolments', [
            'userid' => $userid,
            'enrolid' => $DB->get_field('enrol', 'id', ['courseid' => $courseid, 'enrol' => 'manual'])
        ]);
    }

    /**
     * Get manual enrollment instance for a course.
     *
     * @param int $courseid Course ID
     * @return object|null Manual enrollment instance or null if not found
     */
    private function get_manual_enrollment_instance($courseid) {
        $instances = enrol_get_instances($courseid, true);

        foreach ($instances as $instance) {
            if ($instance->enrol === 'manual') {
                return $instance;
            }
        }

        return null;
    }

    /**
     * Update enrollment status for all records with the given email.
     *
     * @param string $email User email
     * @throws dml_exception If database operation fails
     */
    private function update_enrollment_status($email) {
        global $DB;

        $DB->set_field('importacao_historico', 'status_matricula', '1', ['email' => $email]);
    }
}