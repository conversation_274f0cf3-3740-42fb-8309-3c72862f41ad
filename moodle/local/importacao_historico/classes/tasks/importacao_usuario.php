<?php


namespace local_importacao_historico\tasks;

/**
 * Class importacao
 *
 * @package local_importacao
 * <AUTHOR> Raphael (PH)
 */

use stdClass;
use context_course;

 defined('MOODLE_INTERNAL') || die();

class importacao_usuario extends \core\task\scheduled_task {

    public function get_name()
    {
        return get_string('task_name_user', 'local_importacao_historico');
    }

    public function execute()
    {
        global $CFG, $DB;
        require_once($CFG->libdir . '/filelib.php');
        require_once($CFG->dirroot . '/local/importacao_historico/settings.php');

        $sql = "SELECT 
                    ROW_NUMBER() OVER() AS unique_id,
                    c.id as idcourse, 
                    u.id as iduser, 
                    imp.grade as nota, 
                    imp.status_cadastro as status_cadastro, 
                    imp.status_matricula as status_matricula, 
                    imp.email as email
                FROM {importacao_historico} imp 
                JOIN {course} c ON imp.coursecode = c.idnumber 
                JOIN {user} u ON u.username = imp.username
                group by imp.coursecode, u.id";

        $results = $DB->get_records_sql($sql);

        // Verificar se há resultados
        if (empty($results)) {
            mtrace("Nenhum usuário para inscrever.");
            return;
        }

        foreach ($results as $record) {
            // Obter instância do plugin de inscrição manual
            $enrol_manual = enrol_get_plugin('manual');
            if (!$enrol_manual) {
                mtrace("Plugin de inscrição manual não encontrado.");
                continue;
            }

            // Obter o contexto do curso
            $context = context_course::instance($record->idcourse);

            // Obter a instância do método de inscrição manual no curso
            $instances = enrol_get_instances($record->idcourse, true);
            $manualinstance = null;

            foreach ($instances as $instance) {
                if ($instance->enrol == 'manual') {
                    $manualinstance = $instance;
                    break;
                }
            }

            if (!$manualinstance) {
                mtrace("Instância de inscrição manual não encontrada no curso ID: {$record->idcourse}.");
                continue;
            }

            // Inscrever o usuário no curso
            $enrol_manual->enrol_user($manualinstance, $record->iduser, 5); // 5 é o ID da role (função) de estudante

            // Atualizar o status de matrícula
            $recordsToUpdate = $DB->get_records('importacao_historico', array('email' => $record->email));

            foreach ($recordsToUpdate as $recordToUpdate) {
                $data = new stdClass();
                $data->id = $recordToUpdate->id;
                $data->status_matricula = '1';

                $DB->update_record('importacao_historico', $data);
            }

            mtrace("Usuário {$record->iduser} inscrito no curso {$record->idcourse}.");
        }
    }
}