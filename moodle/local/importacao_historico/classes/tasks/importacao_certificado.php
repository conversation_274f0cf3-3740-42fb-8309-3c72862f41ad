<?php

namespace local_importacao_historico\tasks;

/**
 * Class importacao
 *
 * @package importacao_historico
 * <AUTHOR> Raphael (PH)
 */

use stdClass;

defined('MOODLE_INTERNAL') || die();

class importacao_certificado extends \core\task\scheduled_task
{

    public function get_name()
    {
        return get_string('task_name_certificado', 'local_importacao_historico');
    }

    public function execute() {
        global $CFG, $DB;

        require_once($CFG->dirroot . '/mod/simplecertificate/lib.php');
        require_once($CFG->dirroot . '/mod/simplecertificate/locallib.php');
        require_once($CFG->libdir . '/accesslib.php');

        $IdModuleCert = $DB->get_record('modules', array('name' => 'simplecertificate'));

        $sql = "SELECT 
            ROW_NUMBER() OVER() AS unique_id,
            c.id as idcourse, 
            u.id as iduser, 
            imp.grade as nota, 
            imp.status_cadastro as status_cadastro, 
            imp.status_matricula as status_matricula, 
            mcm.id as cmid,
            imp.email as email,
            imp.username as username,
            ms.id as id_cert,
            UNIX_TIMESTAMP(DATE_ADD(STR_TO_DATE(data_conclusao,'%d/%m/%Y %H:%i'), INTERVAL 3 HOUR)) AS data_conclusao_timestamp
        FROM {importacao_historico} imp 
        JOIN {course} c ON imp.coursecode = c.idnumber 
        JOIN {user} u ON /*u.email = imp.email OR*/ u.username = imp.username
        JOIN {course_modules} mcm ON mcm.course = c.id
        JOIN {simplecertificate} ms on ms.course = c.id 
        WHERE mcm.deletioninprogress = 0 and mcm.module = $IdModuleCert->id and mcm.completionview = 1";

        $results = $DB->get_records_sql($sql);

        foreach ($results as $record) {
            $cm = get_coursemodule_from_id('simplecertificate', $record->cmid, $record->idcourse, false, MUST_EXIST);

            $context = \context_module::instance($record->cmid);

            $course = get_course($cm->course);

            $simplecert = new \simplecertificate($context, $cm, $course); // instância a class do simplecertificate

            $certificado = $simplecert->get_issue($record->iduser); //Obtem o certificado

            $issuecert = $DB->get_record('simplecertificate_issues', array('userid' => $certificado->userid, 'certificateid' => $certificado->certificateid));
            $simplecert->get_issue_file($issuecert); //Gera o PDF

            $issuecert->timecreated = $record->data_conclusao_timestamp;
            
            $DB->update_record('simplecertificate_issues', $issuecert);

            $courseCompletion = $DB->get_record('course_completions', array('userid' => $record->iduser, 'course' => $record->idcourse));
            $courseCompletion->timecompleted = $record->data_conclusao_timestamp;

            $DB->update_record('course_completions', $courseCompletion);

            mtrace('Certificado criado para o usuário: ' . $record->iduser . ' no Curso: ' . $record->idcourse);

        }

    }
}